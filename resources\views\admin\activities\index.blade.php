@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-calendar-alt"></i> จัดการกิจกรรม
            </h1>
            <p class="text-muted mb-0">จัดการข้อมูลกิจกรรมทั้งหมด</p>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createActivityModal">
            <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">กิจกรรมทั้งหมด</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activities->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">เผยแพร่แล้ว</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activities->where('is_published', true)->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">ร่าง</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activities->where('is_published', false)->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">หมวดหมู่</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $categories->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">ค้นหาและกรอง</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="searchInput">ค้นหากิจกรรม</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหาชื่อกิจกรรม...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="categoryFilter">หมวดหมู่</label>
                        <select class="form-control" id="categoryFilter">
                            <option value="">ทุกหมวดหมู่</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="statusFilter">สถานะ</label>
                        <select class="form-control" id="statusFilter">
                            <option value="">ทุกสถานะ</option>
                            <option value="1">เผยแพร่แล้ว</option>
                            <option value="0">ร่าง</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearFilters()">
                                <i class="fas fa-times"></i> ล้าง
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="card shadow mb-4" id="bulkActionsCard" style="display: none;">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <strong id="selectedCount">0</strong> รายการที่เลือก
                </span>
                <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                    <i class="fas fa-trash"></i> ลบที่เลือก
                </button>
            </div>
        </div>
    </div>

    <!-- Activities Grid -->
    <div class="row" id="activitiesGrid">
        @forelse($activities as $activity)
            <div class="col-lg-4 col-md-6 col-12 mb-4 activity-item" 
                 data-activity-id="{{ $activity->id }}"
                 data-category-id="{{ $activity->category_id }}"
                 data-status="{{ $activity->is_published ? '1' : '0' }}">
                <div class="card shadow-sm border-0 h-100 activity-card" style="cursor: pointer;">
                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input activity-checkbox" type="checkbox" value="{{ $activity->id }}"
                                   onclick="event.stopPropagation();">
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                    onclick="event.stopPropagation();">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="loadActivityData({{ $activity->id }})">
                                    <i class="fas fa-edit"></i> แก้ไข</a></li>
                                <li><a class="dropdown-item" href="#" onclick="togglePublish({{ $activity->id }}, {{ $activity->is_published ? 'false' : 'true' }})">
                                    <i class="fas fa-{{ $activity->is_published ? 'eye-slash' : 'eye' }}"></i> 
                                    {{ $activity->is_published ? 'ยกเลิกการเผยแพร่' : 'เผยแพร่' }}</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteActivity({{ $activity->id }})">
                                    <i class="fas fa-trash"></i> ลบ</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Cover Image -->
                    <div class="position-relative">
                        @if($activity->cover_image)
                            <img src="{{ $activity->cover_image_url }}" class="card-img-top" 
                                 style="height: 200px; object-fit: cover;" alt="{{ $activity->title }}">
                        @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                        
                        <!-- Status Badge -->
                        <span class="position-absolute top-0 end-0 m-2">
                            @if($activity->is_published)
                                <span class="badge bg-success">เผยแพร่แล้ว</span>
                            @else
                                <span class="badge bg-warning">ร่าง</span>
                            @endif
                        </span>

                        <!-- Category Badge -->
                        @if($activity->category)
                            <span class="position-absolute top-0 start-0 m-2">
                                <span class="badge" style="background-color: {{ $activity->category->color }};">
                                    {{ $activity->category->name }}
                                </span>
                            </span>
                        @endif
                    </div>

                    <div class="card-body">
                        <h5 class="card-title">{{ $activity->title }}</h5>
                        <p class="card-text text-muted">{{ Str::limit($activity->description, 100) }}</p>
                        
                        <div class="row text-muted small">
                            <div class="col-6">
                                <i class="fas fa-calendar"></i> 
                                {{ $activity->activity_date ? $activity->activity_date->format('d/m/Y') : 'ไม่ระบุ' }}
                            </div>
                            <div class="col-6">
                                <i class="fas fa-images"></i> 
                                {{ $activity->images->count() }} รูป
                            </div>
                        </div>
                        
                        @if($activity->location)
                            <div class="text-muted small mt-1">
                                <i class="fas fa-map-marker-alt"></i> {{ $activity->location }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีกิจกรรม</h5>
                    <p class="text-muted">เริ่มต้นสร้างกิจกรรมแรกของคุณ</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createActivityModal">
                        <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                    </button>
                </div>
            </div>
        @endforelse
    </div>
</div>

<!-- Create/Edit Activity Modal -->
<div class="modal fade" id="createActivityModal" tabindex="-1" aria-labelledby="createActivityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">เพิ่มกิจกรรมใหม่</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="activityForm" enctype="multipart/form-data">
                @csrf
                <input type="hidden" id="activityId" name="activity_id">
                <input type="hidden" id="formMethod" name="_method" value="POST">

                <div class="modal-body">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">รายละเอียด <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                        <select class="form-control" id="category_id" name="category_id" required>
                                            <option value="">เลือกหมวดหมู่</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="activity_date" class="form-label">วันที่จัดกิจกรรม</label>
                                        <input type="date" class="form-control" id="activity_date" name="activity_date">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="location" class="form-label">สถานที่</label>
                                <input type="text" class="form-control" id="location" name="location" placeholder="สถานที่จัดกิจกรรม">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1">
                                    <label class="form-check-label" for="is_published">
                                        เผยแพร่กิจกรรมทันที
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-4">
                            <!-- Cover Image -->
                            <div class="mb-3">
                                <label for="cover_image" class="form-label">รูปปกกิจกรรม</label>
                                <input type="file" class="form-control" id="cover_image" name="cover_image" accept="image/*">
                                <div class="form-text">ขนาดไฟล์ไม่เกิน 2MB (JPG, PNG, GIF, WebP)</div>

                                <!-- Cover Image Preview -->
                                <div id="coverImagePreview" class="mt-2" style="display: none;">
                                    <img id="previewCoverImg" src="" class="img-thumbnail" style="max-width: 100%; height: 150px; object-fit: cover;">
                                    <div class="mt-1">
                                        <small id="currentCoverFileName" class="text-muted"></small>
                                        <button type="button" class="btn btn-sm btn-danger ms-2" id="removeCoverImage">
                                            <i class="fas fa-trash"></i> ลบรูป
                                        </button>
                                    </div>
                                </div>
                                <div id="noCoverImageDisplay" class="mt-2 text-center p-3 border border-dashed rounded">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                    <p class="text-muted mb-0 mt-2">ไม่มีรูปปก</p>
                                </div>
                            </div>

                            <!-- Gallery Images -->
                            <div class="mb-3">
                                <label for="gallery_images" class="form-label">รูปแกลอรี่</label>
                                <input type="file" class="form-control" id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                                <div class="form-text">เลือกได้หลายรูป ขนาดไฟล์ไม่เกิน 2MB ต่อรูป</div>

                                <!-- Gallery Preview -->
                                <div id="galleryPreview" class="mt-2">
                                    <div id="existingGallery" class="row g-2"></div>
                                    <div id="newGalleryPreview" class="row g-2 mt-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Initialize
    updateSelectedCount();

    // Search functionality
    $('#searchInput, #categoryFilter, #statusFilter').on('input change', function() {
        filterActivities();
    });

    // Checkbox functionality
    $(document).on('change', '.activity-checkbox', function() {
        updateSelectedCount();
    });

    // Double click to edit
    $('.activity-card').on('dblclick', function(e) {
        e.preventDefault();
        let activityId = $(this).closest('.activity-item').data('activity-id');
        loadActivityData(activityId);
    });

    // Form submission
    $('#activityForm').on('submit', function(e) {
        e.preventDefault();
        submitActivityForm();
    });

    // Cover image preview
    $('#cover_image').on('change', function() {
        previewCoverImage(this);
    });

    // Gallery images preview
    $('#gallery_images').on('change', function() {
        previewGalleryImages(this);
    });

    // Remove cover image
    $(document).on('click', '#removeCoverImage', function() {
        removeCoverImage();
    });

    // Modal reset
    $('#createActivityModal').on('hidden.bs.modal', function() {
        resetForm();
    });
});

function filterActivities() {
    let searchTerm = $('#searchInput').val().toLowerCase();
    let categoryId = $('#categoryFilter').val();
    let status = $('#statusFilter').val();

    $('.activity-item').each(function() {
        let activityTitle = $(this).find('.card-title').text().toLowerCase();
        let activityDesc = $(this).find('.card-text').text().toLowerCase();
        let activityCategoryId = $(this).data('category-id');
        let activityStatus = $(this).data('status');

        let matchesSearch = activityTitle.includes(searchTerm) || activityDesc.includes(searchTerm);
        let matchesCategory = !categoryId || activityCategoryId == categoryId;
        let matchesStatus = status === '' || activityStatus == status;

        if (matchesSearch && matchesCategory && matchesStatus) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

function clearFilters() {
    $('#searchInput').val('');
    $('#categoryFilter').val('');
    $('#statusFilter').val('');
    filterActivities();
}

function updateSelectedCount() {
    let selectedCount = $('.activity-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);

    if (selectedCount > 0) {
        $('#bulkActionsCard').show();
    } else {
        $('#bulkActionsCard').hide();
    }
}

function loadActivityData(activityId) {
    $.get(`{{ url('admin/activities') }}/${activityId}`, function(data) {
        $('#activityId').val(data.id);
        $('#title').val(data.title);
        $('#description').val(data.description);
        $('#category_id').val(data.category_id);
        $('#activity_date').val(data.activity_date);
        $('#location').val(data.location);
        $('#is_published').prop('checked', data.is_published);

        $('#formMethod').val('PUT');
        $('#modalTitle').text('แก้ไขกิจกรรม');
        $('#submitBtnText').text('อัปเดต');

        // Show cover image if exists
        if (data.cover_image_url) {
            $('#previewCoverImg').attr('src', data.cover_image_url);
            $('#currentCoverFileName').html(`
                <small class="text-info">
                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${data.cover_image_filename || 'cover.jpg'}</strong>
                </small>
            `);
            $('#coverImagePreview').show();
            $('#noCoverImageDisplay').hide();
        } else {
            $('#coverImagePreview').hide();
            $('#noCoverImageDisplay').show();
        }

        // Show existing gallery images
        if (data.images && data.images.length > 0) {
            let galleryHtml = '';
            data.images.forEach(function(image, index) {
                galleryHtml += `
                    <div class="col-6 mb-2">
                        <div class="position-relative">
                            <img src="${image.image_url}" class="img-thumbnail w-100" style="height: 80px; object-fit: cover;">
                            <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                                    onclick="deleteGalleryImage(${image.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            $('#existingGallery').html(galleryHtml);
        } else {
            $('#existingGallery').empty();
        }

        $('#createActivityModal').modal('show');
    }).fail(function(xhr) {
        console.log('Error loading activity data:', xhr);
        Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการโหลดข้อมูล', 'error');
    });
}

function submitActivityForm() {
    let formData = new FormData($('#activityForm')[0]);
    let activityId = $('#activityId').val();
    let method = $('#formMethod').val();
    let url = activityId ? `{{ url('admin/activities') }}/${activityId}` : '{{ route('admin.activities.store') }}';

    $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');

    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด', response.message, 'error');
            }
        },
        error: function(xhr) {
            let message = 'เกิดข้อผิดพลาดในการบันทึก';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            Swal.fire('ข้อผิดพลาด', message, 'error');
        },
        complete: function() {
            $('#submitBtn').prop('disabled', false).html('<i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>');
        }
    });
}

function previewCoverImage(input) {
    if (input.files && input.files[0]) {
        let reader = new FileReader();
        reader.onload = function(e) {
            $('#previewCoverImg').attr('src', e.target.result);
            $('#currentCoverFileName').html(`
                <small class="text-success">
                    <i class="fas fa-file-image"></i> ไฟล์ใหม่: <strong>${input.files[0].name}</strong>
                </small>
            `);
            $('#coverImagePreview').show();
            $('#noCoverImageDisplay').hide();
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function previewGalleryImages(input) {
    if (input.files && input.files.length > 0) {
        let previewHtml = '';
        Array.from(input.files).forEach(function(file, index) {
            let reader = new FileReader();
            reader.onload = function(e) {
                previewHtml += `
                    <div class="col-6 mb-2">
                        <div class="position-relative">
                            <img src="${e.target.result}" class="img-thumbnail w-100" style="height: 80px; object-fit: cover;">
                            <span class="badge bg-primary position-absolute top-0 start-0 m-1">ใหม่</span>
                        </div>
                    </div>
                `;
                if (index === input.files.length - 1) {
                    $('#newGalleryPreview').html(previewHtml);
                }
            };
            reader.readAsDataURL(file);
        });
    }
}

function removeCoverImage() {
    Swal.fire({
        title: 'ลบรูปปก?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบรูปปกนี้?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $('#cover_image').val('');
            $('#coverImagePreview').hide();
            $('#noCoverImageDisplay').show();

            // Add hidden input to mark for removal
            if (!$('#removeCoverImageFlag').length) {
                $('#activityForm').append('<input type="hidden" id="removeCoverImageFlag" name="remove_cover_image" value="1">');
            }
        }
    });
}

function deleteGalleryImage(imageId) {
    Swal.fire({
        title: 'ลบรูปภาพ?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            let activityId = $('#activityId').val();
            $.ajax({
                url: `{{ url('admin/activities') }}/${activityId}/images/${imageId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success');
                        // Remove image from preview
                        $(`button[onclick="deleteGalleryImage(${imageId})"]`).closest('.col-6').remove();
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบรูปภาพ', 'error');
                }
            });
        }
    });
}

function togglePublish(activityId, isPublished) {
    $.ajax({
        url: `{{ url('admin/activities') }}/${activityId}/toggle-publish`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            is_published: isPublished
        },
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด', response.message, 'error');
            }
        },
        error: function(xhr) {
            Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ', 'error');
        }
    });
}

function deleteActivity(activityId) {
    Swal.fire({
        title: 'ลบกิจกรรม?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ url('admin/activities') }}/${activityId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                }
            });
        }
    });
}

function bulkDelete() {
    let selectedIds = $('.activity-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedIds.length === 0) {
        Swal.fire('แจ้งเตือน', 'กรุณาเลือกกิจกรรมที่ต้องการลบ', 'warning');
        return;
    }

    Swal.fire({
        title: 'ลบกิจกรรมที่เลือก?',
        text: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม ${selectedIds.length} รายการ? การดำเนินการนี้ไม่สามารถยกเลิกได้`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route('admin.activities.bulk-delete') }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    activity_ids: selectedIds
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                }
            });
        }
    });
}

function resetForm() {
    $('#activityForm')[0].reset();
    $('#activityId').val('');
    $('#formMethod').val('POST');
    $('#modalTitle').text('เพิ่มกิจกรรมใหม่');
    $('#submitBtnText').text('บันทึก');
    $('#coverImagePreview').hide();
    $('#noCoverImageDisplay').show();
    $('#existingGallery').empty();
    $('#newGalleryPreview').empty();
    $('#removeCoverImageFlag').remove();
}
</script>
@endsection
