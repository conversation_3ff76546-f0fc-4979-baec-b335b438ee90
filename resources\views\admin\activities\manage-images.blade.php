@extends('layouts.admin')

@section('title', 'จัดการรูปภาพ - ' . $activity->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-images"></i> จัดการรูปภาพ - {{ $activity->title }}
                    </h5>
                </div>
                <div class="card-body">
                    @if($images->count() > 0)
                        <div class="row" id="imagesContainer">
                            @foreach($images as $image)
                                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4" id="image-{{ $image->id }}">
                                    <div class="card border-0 shadow-sm">
                                        <div class="position-relative">
                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($image->image_path) }}"
                                                 class="card-img-top" style="height: 200px; object-fit: cover;"
                                                 alt="รูปภาพกิจกรรม"
                                                 onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                            <div class="position-absolute top-0 end-0 m-2">
                                                <button type="button" class="btn btn-sm btn-danger rounded-circle delete-image"
                                                        data-image-id="{{ $image->id }}" data-image-path="{{ $image->image_path }}">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <div class="position-absolute bottom-0 start-0 m-2">
                                                <span class="badge bg-dark">{{ $loop->iteration }}</span>
                                            </div>
                                        </div>
                                        <div class="card-body p-3">
                                            <input type="text" class="form-control form-control-sm image-caption mb-2"
                                                   placeholder="คำบรรยายรูปภาพ..."
                                                   value="{{ $image->caption }}"
                                                   data-image-id="{{ $image->id }}">
                                            <small class="text-muted">ลำดับ: {{ $image->sort_order + 1 }}</small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-success" id="saveImageCaptions">
                                <i class="fas fa-save"></i> บันทึกคำบรรยายรูปภาพ
                            </button>
                            <button type="button" class="btn btn-warning" id="reorderImages">
                                <i class="fas fa-sort"></i> จัดเรียงรูปภาพ
                            </button>
                            <a href="{{ route('admin.activities.edit', $activity) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> กลับไปแก้ไข
                            </a>
                        </div>
                    @else
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-images fa-4x mb-4"></i>
                            <h4>ไม่มีรูปภาพในแกลเลอรี่</h4>
                            <p>กลับไปหน้าแก้ไขเพื่อเพิ่มรูปภาพใหม่</p>
                            <a href="{{ route('admin.activities.edit', $activity) }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> กลับไปแก้ไข
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.card:hover {
    transform: translateY(-2px);
}
.delete-image {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: opacity 0.2s;
}
.delete-image:hover {
    opacity: 1;
}
.cursor-move {
    cursor: move !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
$(document).ready(function() {
    let deletedImages = [];

    // Delete Image
    $(document).on('click', '.delete-image', function() {
        let imageId = $(this).data('image-id');
        let imagePath = $(this).data('image-path');

        Swal.fire({
            title: 'ลบรูปภาพ?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Send AJAX request to delete image
                $.ajax({
                    url: `/admin/activities/{{ $activity->id }}/images/${imageId}`,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        $(`#image-${imageId}`).fadeOut(300, function() {
                            $(this).remove();
                            
                            if ($('#imagesContainer').children().length === 0) {
                                location.reload();
                            }
                        });

                        Swal.fire({
                            title: 'ลบรูปภาพแล้ว!',
                            text: 'รูปภาพถูกลบเรียบร้อยแล้ว',
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    },
                    error: function() {
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด!',
                            text: 'ไม่สามารถลบรูปภาพได้',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                });
            }
        });
    });

    // Save Image Captions
    $('#saveImageCaptions').on('click', function() {
        let captions = [];

        $('.image-caption').each(function() {
            captions.push({
                id: $(this).data('image-id'),
                caption: $(this).val()
            });
        });

        $.ajax({
            url: '{{ route("admin.activities.update-captions", $activity->id) }}',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content'),
                captions: captions
            },
            success: function(response) {
                Swal.fire({
                    title: 'บันทึกสำเร็จ!',
                    text: 'บันทึกคำบรรยายรูปภาพเรียบร้อยแล้ว',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            },
            error: function() {
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: 'ไม่สามารถบันทึกคำบรรยายรูปภาพได้',
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            }
        });
    });

    // Reorder Images
    let sortable = null;

    $('#reorderImages').on('click', function() {
        if (sortable) {
            // Disable sorting and save order
            sortable.destroy();
            sortable = null;
            $(this).html('<i class="fas fa-sort"></i> จัดเรียงรูปภาพ');
            $(this).removeClass('btn-success').addClass('btn-warning');

            // Save new order
            let newOrder = [];
            $('#imagesContainer .card').each(function(index) {
                let imageId = $(this).find('.image-caption').data('image-id');
                newOrder.push({
                    id: imageId,
                    sort_order: index
                });
            });

            $.ajax({
                url: '{{ route("admin.activities.update-order", $activity->id) }}',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    order: newOrder
                },
                success: function(response) {
                    Swal.fire({
                        title: 'บันทึกสำเร็จ!',
                        text: 'บันทึกลำดับรูปภาพเรียบร้อยแล้ว',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                },
                error: function() {
                    Swal.fire({
                        title: 'เกิดข้อผิดพลาด!',
                        text: 'ไม่สามารถบันทึกลำดับรูปภาพได้',
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                }
            });
        } else {
            // Enable sorting
            sortable = new Sortable(document.getElementById('imagesContainer'), {
                animation: 150,
                ghostClass: 'bg-light',
                handle: '.card',
                onStart: function() {
                    $('.card').addClass('cursor-move');
                },
                onEnd: function() {
                    $('.card').removeClass('cursor-move');
                }
            });

            $(this).html('<i class="fas fa-save"></i> บันทึกลำดับรูปภาพ');
            $(this).removeClass('btn-warning').addClass('btn-success');

            Swal.fire({
                title: 'โหมดจัดเรียง',
                text: 'คุณสามารถลากและวางรูปภาพเพื่อจัดเรียงลำดับใหม่ได้',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
});
</script>
@endpush
