@extends('layouts.admin')

@section('title', 'แก้ไขหมวดหมู่กิจกรรม - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-info"></i>แก้ไขหมวดหมู่กิจกรรม
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลหมวดหมู่: {{ $activityCategory->name }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activity-categories.index') }}">
                                <i class="fas fa-tags"></i> จัดการหมวดหมู่กิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขหมวดหมู่
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขหมวดหมู่: {{ $activityCategory->name }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.activity-categories.update', $activityCategory) }}" method="POST" id="categoryForm">
                            @csrf
                            @method('PUT')
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name', $activityCategory->name) }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">คำอธิบาย</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4" placeholder="คำอธิบายเกี่ยวกับหมวดหมู่นี้ (ไม่บังคับ)">{{ old('description', $activityCategory->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="color" class="form-label">สีหมวดหมู่ <span class="text-danger">*</span></label>
                                            <div class="d-flex align-items-center">
                                                <input type="color" class="form-control form-control-color me-2 @error('color') is-invalid @enderror" 
                                                       id="color" name="color" value="{{ old('color', $activityCategory->color) }}" required style="width: 60px;">
                                                <input type="text" class="form-control" id="colorText" placeholder="#007bff" readonly>
                                            </div>
                                            <small class="form-text text-muted">เลือกสีที่จะใช้แสดงหมวดหมู่นี้</small>
                                            @error('color')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                       {{ old('is_active', $activityCategory->is_active) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    เปิดใช้งานหมวดหมู่นี้
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">ตัวอย่างการแสดงผล</label>
                                            <div class="p-3 rounded text-white" id="colorPreview" style="background-color: {{ old('color', $activityCategory->color) }};">
                                                <i class="fas fa-tag me-2"></i>
                                                <span id="previewName">{{ old('name', $activityCategory->name) }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle"></i> สถิติ</h6>
                                            <small>
                                                กิจกรรมในหมวดหมู่นี้: <strong>{{ $activityCategory->activities->count() }}</strong> กิจกรรม<br>
                                                สร้างเมื่อ: <strong>{{ $activityCategory->created_at->format('d/m/Y H:i') }}</strong><br>
                                                แก้ไขล่าสุด: <strong>{{ $activityCategory->updated_at->format('d/m/Y H:i') }}</strong>
                                            </small>
                                        </div>
                                        
                                        @if($activityCategory->activities->count() > 0)
                                            <div class="alert alert-warning">
                                                <h6><i class="fas fa-exclamation-triangle"></i> คำเตือน</h6>
                                                <small>
                                                    หมวดหมู่นี้มีกิจกรรม {{ $activityCategory->activities->count() }} กิจกรรม<br>
                                                    การเปลี่ยนแปลงจะส่งผลต่อกิจกรรมเหล่านั้น
                                                </small>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.activity-categories.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตหมวดหมู่
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Delete Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trash"></i> ลบหมวดหมู่
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($activityCategory->activities->count() > 0)
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>ไม่สามารถลบได้!</strong> หมวดหมู่นี้มีกิจกรรม {{ $activityCategory->activities->count() }} กิจกรรม
                                    กรุณาย้ายกิจกรรมไปหมวดหมู่อื่นก่อน หรือลบกิจกรรมเหล่านั้นออก
                                </div>
                            @else
                                <p class="text-muted">การลบหมวดหมู่นี้จะไม่สามารถกู้คืนได้</p>
                                <form action="{{ route('admin.activity-categories.destroy', $activityCategory) }}" method="POST" id="deleteForm">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้?')">
                                        <i class="fas fa-trash"></i> ลบหมวดหมู่นี้
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                    
                    <!-- Activities in this category -->
                    @if($activityCategory->activities->count() > 0)
                        <div class="card shadow-sm border-0 mt-3">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-images"></i> กิจกรรมในหมวดหมู่นี้ ({{ $activityCategory->activities->count() }} กิจกรรม)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    @foreach($activityCategory->activities->take(6) as $activity)
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <img src="{{ $activity->cover_image_url }}" class="card-img-top" style="height: 120px; object-fit: cover;" alt="{{ $activity->title }}">
                                                <div class="card-body p-2">
                                                    <h6 class="card-title mb-1">{{ Str::limit($activity->title, 30) }}</h6>
                                                    <small class="text-muted">{{ $activity->formatted_date ?: 'ไม่ระบุวันที่' }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                @if($activityCategory->activities->count() > 6)
                                    <div class="text-center">
                                        <a href="{{ route('admin.activities.index') }}?category={{ $activityCategory->id }}" class="btn btn-outline-primary btn-sm">
                                            ดูทั้งหมด {{ $activityCategory->activities->count() }} กิจกรรม
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus, .form-select:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23,162,184,.25);
}
.form-control-color {
    height: 38px;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Color picker change
    $('#color').on('change', function() {
        let color = $(this).val();
        $('#colorText').val(color);
        $('#colorPreview').css('background-color', color);
    });

    // Name change for preview
    $('#name').on('keyup', function() {
        let name = $(this).val() || 'ชื่อหมวดหมู่';
        $('#previewName').text(name);
    });

    // Form validation
    $('#categoryForm').on('submit', function(e) {
        let isValid = true;
        
        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Validate name
        if (!$('#name').val().trim()) {
            $('#name').addClass('is-invalid');
            $('#name').after('<div class="invalid-feedback">กรุณากรอกชื่อหมวดหมู่</div>');
            isValid = false;
        }
        
        // Validate color
        if (!$('#color').val()) {
            $('#color').addClass('is-invalid');
            $('#color').after('<div class="invalid-feedback">กรุณาเลือกสีหมวดหมู่</div>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });

    // Initialize color text on page load
    $('#colorText').val($('#color').val());
});
</script>
@endpush

@endsection
