@extends('layouts.admin')

@section('title', 'เพิ่มหมวดหมู่กิจกรรมใหม่ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-plus me-2 text-info"></i>เพิ่มหมวดหมู่กิจกรรมใหม่
                    </h1>
                    <p class="text-muted">เพิ่มหมวดหมู่ใหม่สำหรับจัดกลุ่มกิจกรรม</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.activity-categories.index') }}">
                                <i class="fas fa-tags"></i> จัดการหมวดหมู่กิจกรรม
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus"></i> เพิ่มหมวดหมู่กิจกรรมใหม่
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.activity-categories.store') }}" method="POST" id="categoryForm">
                            @csrf
                            
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">คำอธิบาย</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="4" placeholder="คำอธิบายเกี่ยวกับหมวดหมู่นี้ (ไม่บังคับ)">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="color" class="form-label">สีหมวดหมู่ <span class="text-danger">*</span></label>
                                            <div class="d-flex align-items-center">
                                                <input type="color" class="form-control form-control-color me-2 @error('color') is-invalid @enderror" 
                                                       id="color" name="color" value="{{ old('color', '#007bff') }}" required style="width: 60px;">
                                                <input type="text" class="form-control" id="colorText" placeholder="#007bff" readonly>
                                            </div>
                                            <small class="form-text text-muted">เลือกสีที่จะใช้แสดงหมวดหมู่นี้</small>
                                            @error('color')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    เปิดใช้งานหมวดหมู่นี้ทันที
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">ตัวอย่างการแสดงผล</label>
                                            <div class="p-3 rounded text-white" id="colorPreview" style="background-color: {{ old('color', '#007bff') }};">
                                                <i class="fas fa-tag me-2"></i>
                                                <span id="previewName">{{ old('name', 'ชื่อหมวดหมู่') }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle"></i> คำแนะนำ</h6>
                                            <small>
                                                • ใช้ชื่อที่สั้นและเข้าใจง่าย<br>
                                                • เลือกสีที่แตกต่างกันเพื่อง่ายต่อการแยกแยะ<br>
                                                • สามารถแก้ไขได้ภายหลัง
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.activity-categories.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-undo"></i> รีเซ็ต
                                        </button>
                                        <button type="submit" class="btn btn-info">
                                            <i class="fas fa-save"></i> บันทึกหมวดหมู่
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus, .form-select:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23,162,184,.25);
}
.form-control-color {
    height: 38px;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Color picker change
    $('#color').on('change', function() {
        let color = $(this).val();
        $('#colorText').val(color);
        $('#colorPreview').css('background-color', color);
    });

    // Name change for preview
    $('#name').on('keyup', function() {
        let name = $(this).val() || 'ชื่อหมวดหมู่';
        $('#previewName').text(name);
    });

    // Form validation
    $('#categoryForm').on('submit', function(e) {
        let isValid = true;
        
        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Validate name
        if (!$('#name').val().trim()) {
            $('#name').addClass('is-invalid');
            $('#name').after('<div class="invalid-feedback">กรุณากรอกชื่อหมวดหมู่</div>');
            isValid = false;
        }
        
        // Validate color
        if (!$('#color').val()) {
            $('#color').addClass('is-invalid');
            $('#color').after('<div class="invalid-feedback">กรุณาเลือกสีหมวดหมู่</div>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });

    // Initialize color text on page load
    $('#colorText').val($('#color').val());

    // Reset form
    $('button[type="reset"]').on('click', function() {
        setTimeout(function() {
            $('#color').val('#007bff');
            $('#colorText').val('#007bff');
            $('#colorPreview').css('background-color', '#007bff');
            $('#previewName').text('ชื่อหมวดหมู่');
        }, 10);
    });
});
</script>
@endpush

@endsection
