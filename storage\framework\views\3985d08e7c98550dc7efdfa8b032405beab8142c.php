<?php $__env->startSection('title', 'จัดการหมวดหมู่กิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-tags"></i> จัดการหมวดหมู่กิจกรรม
            </h1>
            <p class="text-muted mb-0">จัดการหมวดหมู่สำหรับการจัดกลุ่มกิจกรรม</p>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
            <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">หมวดหมู่ทั้งหมด</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($categories->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">ใช้งานอยู่</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($categories->where('is_active', true)->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">ปิดใช้งาน</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($categories->where('is_active', false)->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">กิจกรรมทั้งหมด</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($categories->sum('activities_count')); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">ค้นหาและกรอง</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="searchInput">ค้นหาหมวดหมู่</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหาชื่อหมวดหมู่...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="statusFilter">สถานะ</label>
                        <select class="form-control" id="statusFilter">
                            <option value="">ทุกสถานะ</option>
                            <option value="1">ใช้งานอยู่</option>
                            <option value="0">ปิดใช้งาน</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearFilters()">
                                <i class="fas fa-times"></i> ล้าง
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="card shadow mb-4" id="bulkActionsCard" style="display: none;">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <strong id="selectedCount">0</strong> รายการที่เลือก
                </span>
                <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                    <i class="fas fa-trash"></i> ลบที่เลือก
                </button>
            </div>
        </div>
    </div>

    <!-- Categories Grid -->
    <div class="row" id="categoriesGrid">
        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-lg-4 col-md-6 col-12 mb-4 category-item" 
                 data-category-id="<?php echo e($category->id); ?>"
                 data-status="<?php echo e($category->is_active ? '1' : '0'); ?>">
                <div class="card shadow-sm border-0 h-100 category-card" style="cursor: pointer;">
                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input category-checkbox" type="checkbox" value="<?php echo e($category->id); ?>"
                                   onclick="event.stopPropagation();">
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                    onclick="event.stopPropagation();">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="loadCategoryData(<?php echo e($category->id); ?>)">
                                    <i class="fas fa-edit"></i> แก้ไข</a></li>
                                <li><a class="dropdown-item" href="#" onclick="toggleStatus(<?php echo e($category->id); ?>, <?php echo e($category->is_active ? 'false' : 'true'); ?>)">
                                    <i class="fas fa-<?php echo e($category->is_active ? 'eye-slash' : 'eye'); ?>"></i> 
                                    <?php echo e($category->is_active ? 'ปิดใช้งาน' : 'เปิดใช้งาน'); ?></a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory(<?php echo e($category->id); ?>)">
                                    <i class="fas fa-trash"></i> ลบ</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Category Color -->
                    <div class="position-relative">
                        <div class="card-img-top d-flex align-items-center justify-content-center" 
                             style="height: 120px; background: linear-gradient(135deg, <?php echo e($category->color); ?> 0%, <?php echo e($category->color); ?>88 100%);">
                            <i class="fas fa-tag fa-3x text-white"></i>
                        </div>
                        
                        <!-- Status Badge -->
                        <span class="position-absolute top-0 end-0 m-2">
                            <?php if($category->is_active): ?>
                                <span class="badge bg-success">ใช้งานอยู่</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">ปิดใช้งาน</span>
                            <?php endif; ?>
                        </span>

                        <!-- Activities Count Badge -->
                        <span class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-primary">
                                <?php echo e($category->activities_count); ?> กิจกรรม
                            </span>
                        </span>
                    </div>

                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($category->name); ?></h5>
                        <p class="card-text text-muted"><?php echo e($category->description ?: 'ไม่มีรายละเอียด'); ?></p>
                        
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="color-preview me-2" 
                                     style="width: 20px; height: 20px; background-color: <?php echo e($category->color); ?>; border-radius: 50%; border: 2px solid #dee2e6;"></div>
                                <small class="text-muted"><?php echo e($category->color); ?></small>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-calendar-plus"></i> 
                                <?php echo e($category->created_at->format('d/m/Y')); ?>

                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีหมวดหมู่</h5>
                    <p class="text-muted">เริ่มต้นสร้างหมวดหมู่แรกของคุณ</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                        <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Create/Edit Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1" aria-labelledby="createCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">เพิ่มหมวดหมู่ใหม่</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="categoryForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="categoryId" name="category_id">
                <input type="hidden" id="formMethod" name="_method" value="POST">

                <div class="modal-body">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">รายละเอียด</label>
                                <textarea class="form-control" id="description" name="description" rows="3" placeholder="รายละเอียดของหมวดหมู่ (ไม่บังคับ)"></textarea>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        เปิดใช้งานหมวดหมู่
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="color" class="form-label">สีหมวดหมู่ <span class="text-danger">*</span></label>
                                <input type="color" class="form-control form-control-color" id="color" name="color" value="#007bff" required>
                                <div class="form-text">เลือกสีที่จะใช้แสดงหมวดหมู่นี้</div>
                            </div>

                            <!-- Color Preview -->
                            <div class="mb-3">
                                <label class="form-label">ตัวอย่างสี</label>
                                <div class="card" id="colorPreview" style="border: 2px solid #007bff;">
                                    <div class="card-header text-white text-center" style="background: linear-gradient(135deg, #007bff 0%, #007bff88 100%);">
                                        <i class="fas fa-tag fa-2x"></i>
                                        <div class="mt-2">
                                            <span class="badge bg-light text-dark">ตัวอย่าง</span>
                                        </div>
                                    </div>
                                    <div class="card-body text-center">
                                        <h6 class="card-title mb-1">ชื่อหมวดหมู่</h6>
                                        <small class="text-muted">ตัวอย่างการแสดงผล</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Initialize
    updateSelectedCount();
    updateColorPreview();

    // Search functionality
    $('#searchInput, #statusFilter').on('input change', function() {
        filterCategories();
    });

    // Checkbox functionality
    $(document).on('change', '.category-checkbox', function() {
        updateSelectedCount();
    });

    // Double click to edit
    $('.category-card').on('dblclick', function(e) {
        e.preventDefault();
        let categoryId = $(this).closest('.category-item').data('category-id');
        loadCategoryData(categoryId);
    });

    // Form submission
    $('#categoryForm').on('submit', function(e) {
        e.preventDefault();
        submitCategoryForm();
    });

    // Color change preview
    $('#color').on('input change', function() {
        updateColorPreview();
    });

    // Modal reset
    $('#createCategoryModal').on('hidden.bs.modal', function() {
        resetForm();
    });
});

function filterCategories() {
    let searchTerm = $('#searchInput').val().toLowerCase();
    let status = $('#statusFilter').val();

    $('.category-item').each(function() {
        let categoryName = $(this).find('.card-title').text().toLowerCase();
        let categoryDesc = $(this).find('.card-text').text().toLowerCase();
        let categoryStatus = $(this).data('status');

        let matchesSearch = categoryName.includes(searchTerm) || categoryDesc.includes(searchTerm);
        let matchesStatus = status === '' || categoryStatus == status;

        if (matchesSearch && matchesStatus) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

function clearFilters() {
    $('#searchInput').val('');
    $('#statusFilter').val('');
    filterCategories();
}

function updateSelectedCount() {
    let selectedCount = $('.category-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);

    if (selectedCount > 0) {
        $('#bulkActionsCard').show();
    } else {
        $('#bulkActionsCard').hide();
    }
}

function updateColorPreview() {
    let color = $('#color').val();
    let previewCard = $('#colorPreview');

    previewCard.css('border-color', color);
    previewCard.find('.card-header').css('background', `linear-gradient(135deg, ${color} 0%, ${color}88 100%)`);
}

function loadCategoryData(categoryId) {
    $.get(`<?php echo e(url('admin/activity-categories')); ?>/${categoryId}`, function(data) {
        $('#categoryId').val(data.id);
        $('#name').val(data.name);
        $('#description').val(data.description);
        $('#color').val(data.color);
        $('#is_active').prop('checked', data.is_active);

        $('#formMethod').val('PUT');
        $('#modalTitle').text('แก้ไขหมวดหมู่');
        $('#submitBtnText').text('อัปเดต');

        updateColorPreview();
        $('#createCategoryModal').modal('show');
    }).fail(function(xhr) {
        console.log('Error loading category data:', xhr);
        Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการโหลดข้อมูล', 'error');
    });
}

function submitCategoryForm() {
    let formData = new FormData($('#categoryForm')[0]);
    let categoryId = $('#categoryId').val();
    let method = $('#formMethod').val();
    let url = categoryId ? `<?php echo e(url('admin/activity-categories')); ?>/${categoryId}` : '<?php echo e(route('admin.activity-categories.store')); ?>';

    $('#submitBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');

    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด', response.message, 'error');
            }
        },
        error: function(xhr) {
            let message = 'เกิดข้อผิดพลาดในการบันทึก';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            Swal.fire('ข้อผิดพลาด', message, 'error');
        },
        complete: function() {
            $('#submitBtn').prop('disabled', false).html('<i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>');
        }
    });
}

function toggleStatus(categoryId, isActive) {
    $.ajax({
        url: `<?php echo e(url('admin/activity-categories')); ?>/${categoryId}/toggle-status`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            is_active: isActive
        },
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด', response.message, 'error');
            }
        },
        error: function(xhr) {
            Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ', 'error');
        }
    });
}

function deleteCategory(categoryId) {
    Swal.fire({
        title: 'ลบหมวดหมู่?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `<?php echo e(url('admin/activity-categories')); ?>/${categoryId}`,
                type: 'DELETE',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบหมวดหมู่', 'error');
                }
            });
        }
    });
}

function bulkDelete() {
    let selectedIds = $('.category-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedIds.length === 0) {
        Swal.fire('แจ้งเตือน', 'กรุณาเลือกหมวดหมู่ที่ต้องการลบ', 'warning');
        return;
    }

    Swal.fire({
        title: 'ลบหมวดหมู่ที่เลือก?',
        text: `คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่ ${selectedIds.length} รายการ? การดำเนินการนี้ไม่สามารถยกเลิกได้`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route('admin.activity-categories.bulk-delete')); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    category_ids: selectedIds
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบหมวดหมู่', 'error');
                }
            });
        }
    });
}

function resetForm() {
    $('#categoryForm')[0].reset();
    $('#categoryId').val('');
    $('#formMethod').val('POST');
    $('#modalTitle').text('เพิ่มหมวดหมู่ใหม่');
    $('#submitBtnText').text('บันทึก');
    $('#color').val('#007bff');
    $('#is_active').prop('checked', true);
    updateColorPreview();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activity-categories/index.blade.php ENDPATH**/ ?>